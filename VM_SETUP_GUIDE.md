# 🖥️ VM虛擬機運行指南

## 📋 概述
此指南說明如何在虛擬機環境中運行MapleStory自動練級程式。

## 🚀 快速開始

### 1. 啟用VM模式
在命令行中添加 `--vm_mode` 參數：

```bash
python mapleStoryAutoLevelUp.py --vm_mode --map your_map --monsters your_monsters
```

### 2. 配置遊戲視窗
確保遊戲運行在**視窗模式**（不是全螢幕），並使用最小解析度。

## ⚙️ 配置設定

### 自動檢測模式（推薦）
程式會自動尋找遊戲視窗，無需額外設定。

### 手動區域模式（如果自動檢測失敗）
如果自動檢測失敗，可以在 `config/config_default.yaml` 中設定手動區域：

```yaml
game_window:
  title: "MapleStory Worlds-Artale (繁體中文版)"
  size: [752, 1282]
  ratio_tolerance: 0.08
  # 啟用手動區域設定
  manual_region:
    left: 100      # 遊戲視窗左邊位置
    top: 100       # 遊戲視窗上邊位置  
    width: 1282    # 遊戲視窗寬度
    height: 752    # 遊戲視窗高度
```

## 🔧 如何找到正確的區域座標

### 方法1：使用截圖工具
1. 開啟遊戲視窗
2. 使用截圖工具（如Windows的剪取工具）
3. 選取遊戲視窗區域，記錄座標和尺寸

### 方法2：使用程式碼檢測
在Python中運行以下程式碼：

```python
import pygetwindow as gw

# 列出所有視窗
windows = gw.getAllWindows()
for w in windows:
    if "MapleStory" in w.title:
        print(f"Title: {w.title}")
        print(f"Position: left={w.left}, top={w.top}")
        print(f"Size: width={w.width}, height={w.height}")
```

## 🎯 VM模式特點

### ✅ 優點
- **更好的兼容性**：使用螢幕區域捕獲而非視窗特定捕獲
- **自動恢復**：視窗移動後自動重新檢測
- **容錯機制**：提供多種fallback選項
- **完整功能支援**：RUNE、自動換頻道、自動組隊都已優化

### ✅ 支援的功能
- **RUNE檢測和解題**：✅ 完全支援（包括緊急檢測）
- **自動換頻道**：✅ 支援（使用手動區域座標）
- **自動組隊**：✅ 支援（使用手動區域座標）
- **怪物檢測和攻擊**：✅ 完全支援
- **路線跟隨**：✅ 完全支援

### ⚠️ 注意事項
- 確保遊戲視窗不被其他視窗遮擋
- 避免在程式運行時移動遊戲視窗
- VM中可能需要較低的FPS設定以提高穩定性
- 自動換頻道和組隊功能需要準確的視窗位置

## 🐛 故障排除

### 問題1：找不到遊戲視窗
**解決方案：**
1. 確認遊戲視窗標題是否正確
2. 嘗試使用手動區域設定
3. 檢查遊戲是否在視窗模式

### 問題2：捕獲的畫面不正確
**解決方案：**
1. 調整 `manual_region` 的座標
2. 確保遊戲解析度設定正確
3. 檢查VM的顯示設定

### 問題3：性能問題
**解決方案：**
1. 降低FPS限制：
```yaml
system:
  fps_limit_window_capturor: 10  # 降低到10 FPS
  fps_limit_main: 5              # 降低主循環FPS
```

## 📞 技術支援
如果遇到問題，請提供：
1. VM軟體和版本
2. 遊戲視窗設定
3. 錯誤訊息截圖
4. 配置文件內容
