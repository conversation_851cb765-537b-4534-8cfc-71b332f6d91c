# 🖥️ VM虛擬機運行指南

## 📋 概述
此指南說明如何在虛擬機環境中運行MapleStory自動練級程式。

## 🚀 快速開始

### 1. 啟用VM模式
在命令行中添加 `--vm_mode` 參數：

```bash
python mapleStoryAutoLevelUp.py --vm_mode --map your_map --monsters your_monsters
```

### 2. 配置遊戲視窗
確保遊戲運行在**視窗模式**（不是全螢幕），並使用最小解析度。

## ⚙️ 智能自動檢測

### 🤖 多層檢測策略
程式使用智能檢測系統，會自動嘗試以下方法找到遊戲視窗：

1. **精確標題匹配**：尋找完全符合的視窗標題
2. **關鍵字匹配**：搜尋包含"MapleStory"、"Artale"、"楓之谷"的視窗
3. **尺寸智能匹配**：根據視窗大小和預期遊戲尺寸進行匹配
4. **活動視窗檢測**：如果其他方法失敗，使用當前活動的遊戲視窗

### ✅ 無需手動設定
- 程式會自動找到最合適的遊戲視窗
- 支援視窗移動後的自動重新檢測
- 智能評分系統選擇最佳匹配視窗

## 🎯 VM模式特點

### ✅ 優點
- **更好的兼容性**：使用螢幕區域捕獲而非視窗特定捕獲
- **自動恢復**：視窗移動後自動重新檢測
- **容錯機制**：提供多種fallback選項
- **完整功能支援**：RUNE、自動換頻道、自動組隊都已優化

### ✅ 支援的功能
- **RUNE檢測和解題**：✅ 完全支援（包括緊急檢測）
- **自動換頻道**：✅ 支援（使用手動區域座標）
- **自動組隊**：✅ 支援（使用手動區域座標）
- **怪物檢測和攻擊**：✅ 完全支援
- **路線跟隨**：✅ 完全支援

### ⚠️ 注意事項
- 確保遊戲視窗不被其他視窗遮擋
- 避免在程式運行時移動遊戲視窗
- VM中可能需要較低的FPS設定以提高穩定性
- 自動換頻道和組隊功能需要準確的視窗位置

## 🐛 故障排除

### 問題1：找不到遊戲視窗
**解決方案：**
1. 確保遊戲在視窗模式運行
2. 檢查遊戲視窗標題是否包含"MapleStory"或"Artale"
3. 嘗試將遊戲視窗設為活動視窗（點擊遊戲視窗）
4. 檢查程式日誌中的視窗檢測訊息

### 問題2：點擊位置不準確
**解決方案：**
1. 確保遊戲視窗沒有被其他視窗遮擋
2. 避免在程式運行時移動遊戲視窗
3. 檢查VM的顯示縮放設定（建議100%）

### 問題3：性能問題
**解決方案：**
1. 降低FPS限制：
```yaml
system:
  fps_limit_window_capturor: 10  # 降低到10 FPS
  fps_limit_main: 5              # 降低主循環FPS
```

### 問題4：視窗檢測日誌
程式會輸出詳細的檢測日誌，例如：
```
[find_game_window_smart] Found exact match: MapleStory Worlds-Artale
[find_game_window_smart] Found partial match: 楓之谷世界
[VM Capturor] Updated region: {'left': 100, 'top': 50, 'width': 1282, 'height': 752}
```

## 📞 技術支援
如果遇到問題，請提供：
1. VM軟體和版本
2. 遊戲視窗設定和標題
3. 程式日誌中的視窗檢測訊息
4. 錯誤訊息截圖
