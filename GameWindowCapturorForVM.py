'''
VM-compatible Game Window Capturor using screen region capture
This version works better in virtual machines where window-specific capture might fail
'''
# Standard import
import time
import threading

# Library import
import mss
import cv2
import numpy as np
import pygetwindow as gw

# Local import
from logger import logger
from util import is_img_16_to_9

class GameWindowCapturor:
    '''
    VM-compatible GameWindowCapturor using screen region capture
    '''
    def __init__(self, cfg, args):
        self.cfg = cfg
        self.frame = None
        self.lock = threading.Lock()
        
        self.window_title = cfg["game_window"]["title"]
        
        self.fps = 0
        self.fps_limit = cfg["system"]["fps_limit_window_capturor"]
        self.t_last_run = 0.0
        
        # Use mss for screen region capture (VM-compatible)
        self.capture = mss.mss()
        
        # Get game window region
        self.update_window_region()
        
        # Start game window capture thread
        threading.Thread(target=self.start_capture, daemon=True).start()
        
        # Wait for frame initialization
        time.sleep(0.1)
        while self.frame is None:
            self.limit_fps()
            
        # Check window size/ratio
        if args is not None and args.aux:
            # Check if game window ratio is 16:9
            if not is_img_16_to_9(self.frame, cfg):
                logger.error(f"Invalid window ratio: {self.frame.shape[:2]} (expected 16:9 window)")
                logger.error("Please use windowed mode & smallest resolution.")
                raise RuntimeError(f"Unexpected window ratio: {self.frame.shape[:2]}")
        else:
            if self.frame.shape[:2] != cfg["game_window"]["size"]:
                logger.error(f"Invalid window size: {self.frame.shape[:2]} (expected {cfg['game_window']['size']})")
                logger.error("Please use windowed mode & smallest resolution.")
                raise RuntimeError(f"Unexpected window size: {self.frame.shape[:2]}")
    
    def start_capture(self):
        '''
        Start screen capture loop
        '''
        while True:
            try:
                # Update window region periodically
                self.update_window_region()
                
                # Capture frame
                self.capture_frame()
                
                # Limit FPS to save system resources
                self.limit_fps()
            except Exception as e:
                logger.error(f"Capture error: {e}")
                time.sleep(0.1)
    
    def update_window_region(self):
        '''
        Update window region by finding the game window
        '''
        try:
            # Try to find the game window
            windows = gw.getWindowsWithTitle(self.window_title)
            if not windows:
                # If exact title not found, try partial match
                all_windows = gw.getAllWindows()
                windows = [w for w in all_windows if self.window_title in w.title]
            
            if not windows:
                # Fallback: use manual region if specified in config
                if "manual_region" in self.cfg["game_window"]:
                    self.region = self.cfg["game_window"]["manual_region"]
                    logger.info(f"Using manual region: {self.region}")
                    return
                else:
                    raise RuntimeError(f"Cannot find window: {self.window_title}")
            
            # Use the first matching window
            window = windows[0]
            self.region = {
                "left": window.left,
                "top": window.top,
                "width": window.width,
                "height": window.height
            }
            
        except Exception as e:
            logger.error(f"Failed to update window region: {e}")
            # Use last known region or fallback
            if not hasattr(self, 'region'):
                # Default fallback region (adjust as needed)
                self.region = {
                    "left": 100,
                    "top": 100, 
                    "width": 1282,
                    "height": 752
                }
                logger.warning(f"Using fallback region: {self.region}")
    
    def capture_frame(self):
        '''
        Capture current game region frame
        '''
        try:
            img = self.capture.grab(self.region)
            frame = np.array(img)
            
            with self.lock:
                self.frame = frame
                
        except Exception as e:
            logger.error(f"Frame capture failed: {e}")
    
    def get_frame(self):
        '''
        Safely get latest game window frame
        '''
        with self.lock:
            if self.frame is None:
                return None
            # Convert BGRA to BGR
            return cv2.cvtColor(self.frame, cv2.COLOR_BGRA2BGR)
    
    def on_closed(self):
        '''
        Capture closed callback
        '''
        logger.warning("Capture session closed.")
        cv2.destroyAllWindows()
    
    def limit_fps(self):
        '''
        Limit FPS to save system resources
        '''
        # If the loop finished early, sleep to maintain target FPS
        target_duration = 1.0 / self.fps_limit  # seconds per frame
        frame_duration = time.time() - self.t_last_run
        if frame_duration < target_duration:
            time.sleep(target_duration - frame_duration)
        
        # Update FPS
        self.fps = round(1.0 / (time.time() - self.t_last_run))
        self.t_last_run = time.time()
