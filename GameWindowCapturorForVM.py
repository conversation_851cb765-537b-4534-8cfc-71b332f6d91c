'''
VM-compatible Game Window Capturor using screen region capture
This version works better in virtual machines where window-specific capture might fail
'''
# Standard import
import time
import threading

# Library import
import mss
import cv2
import numpy as np
import pygetwindow as gw
import pyautogui

# Local import
from logger import logger
from util import is_img_16_to_9

class GameWindowCapturor:
    '''
    VM-compatible GameWindowCapturor using screen region capture
    '''
    def __init__(self, cfg, args):
        self.cfg = cfg
        self.frame = None
        self.lock = threading.Lock()
        
        self.window_title = cfg["game_window"]["title"]
        
        self.fps = 0
        self.fps_limit = cfg["system"]["fps_limit_window_capturor"]
        self.t_last_run = 0.0
        
        # Use mss for screen region capture (VM-compatible)
        # Initialize capture in main thread to avoid threading issues
        self.capture = None
        
        # Get game window region
        self.update_window_region()
        
        # Start game window capture thread
        threading.Thread(target=self.start_capture, daemon=True).start()
        
        # Wait for frame initialization
        time.sleep(0.1)
        while self.frame is None:
            self.limit_fps()
            
        # Check window size/ratio with VM mode flexibility
        actual_size = self.frame.shape[:2]
        expected_size = cfg["game_window"]["size"]

        if args is not None and args.aux:
            # Check if game window ratio is 16:9
            if not is_img_16_to_9(self.frame, cfg):
                logger.error(f"Invalid window ratio: {actual_size} (expected 16:9 window)")
                logger.error("Please use windowed mode & smallest resolution.")
                raise RuntimeError(f"Unexpected window ratio: {actual_size}")
        else:
            # VM mode: allow flexibility in window size
            tolerance = cfg["game_window"].get("vm_size_tolerance", [10, 20])
            height_diff = abs(actual_size[0] - expected_size[0])
            width_diff = abs(actual_size[1] - expected_size[1])

            if height_diff <= tolerance[0] and width_diff <= tolerance[1]:
                logger.info(f"VM Mode: Window size {actual_size} is within tolerance of expected {expected_size}")
            else:
                logger.warning(f"VM Mode: Window size {actual_size} differs from expected {expected_size}")
                logger.info("VM Mode: Auto-adjusting to actual window size...")

            # Always update config to match actual size in VM mode for consistency
            cfg["game_window"]["size"] = actual_size
            logger.info(f"VM Mode: Updated config window size to {actual_size}")
    
    def start_capture(self):
        '''
        Start screen capture loop
        '''
        while True:
            try:
                # Update window region periodically
                self.update_window_region()
                
                # Capture frame
                self.capture_frame()
                
                # Limit FPS to save system resources
                self.limit_fps()
            except Exception as e:
                logger.error(f"Capture error: {e}")
                time.sleep(0.1)
    
    def find_game_window_smart(self):
        '''
        Smart game window detection with multiple strategies
        '''
        try:
            # Strategy 1: Exact title match
            windows = gw.getWindowsWithTitle(self.window_title)
            if windows:
                window = windows[0]
                logger.info(f"[VM Capturor] Found exact match: {window.title}")
                return window

            # Strategy 2: Partial title match with key parts
            all_windows = gw.getAllWindows()
            key_parts = ["MapleStory", "Artale", "楓之谷", "Worlds"]
            partial_matches = []

            for window in all_windows:
                if window.title and any(part in window.title for part in key_parts):
                    partial_matches.append(window)

            if partial_matches:
                # Score windows by title relevance and size
                def score_window(w):
                    title_score = sum(1 for part in key_parts if part in w.title)
                    size_score = w.width * w.height / 1000000
                    return title_score + size_score

                best_window = max(partial_matches, key=score_window)
                logger.info(f"[VM Capturor] Found partial match: {best_window.title}")
                return best_window

            # Strategy 3: Look for game-sized windows
            game_windows = []
            for window in all_windows:
                # Look for windows with reasonable game dimensions
                if (800 <= window.width <= 2000 and
                    600 <= window.height <= 1500 and
                    window.title and len(window.title) > 3):
                    game_windows.append(window)

            if game_windows:
                # Prefer window closest to expected game size
                expected_size = self.cfg["game_window"]["size"]
                expected_area = expected_size[0] * expected_size[1]

                def size_similarity(w):
                    actual_area = w.width * w.height
                    return 1.0 / (1.0 + abs(actual_area - expected_area) / expected_area)

                best_window = max(game_windows, key=size_similarity)
                logger.info(f"[VM Capturor] Found by size similarity: {best_window.title}")
                return best_window

            # Strategy 4: Use active window if reasonable
            active_window = gw.getActiveWindow()
            if (active_window and
                active_window.width > 800 and active_window.height > 600):
                logger.info(f"[VM Capturor] Using active window: {active_window.title}")
                return active_window

            return None

        except Exception as e:
            logger.error(f"[VM Capturor] Error in smart detection: {e}")
            return None

    def update_window_region(self):
        '''
        Update window region using smart detection
        '''
        try:
            window = self.find_game_window_smart()

            if window:
                self.region = {
                    "left": window.left,
                    "top": window.top,
                    "width": window.width,
                    "height": window.height
                }
                logger.debug(f"[VM Capturor] Updated region: {self.region}")
            else:
                # Fallback: use manual region if specified
                if "manual_region" in self.cfg["game_window"]:
                    self.region = self.cfg["game_window"]["manual_region"]
                    logger.info(f"[VM Capturor] Using manual region: {self.region}")
                elif not hasattr(self, 'region'):
                    # Last resort: reasonable default
                    self.region = {
                        "left": 100,
                        "top": 100,
                        "width": 1282,
                        "height": 752
                    }
                    logger.warning(f"[VM Capturor] Using default region: {self.region}")
                # If we already have a region, keep using it

        except Exception as e:
            logger.error(f"[VM Capturor] Failed to update window region: {e}")
            # Keep existing region if available
    
    def capture_frame(self):
        '''
        Capture current game region frame with fallback methods
        '''
        frame = None

        # Method 1: Try mss (fastest)
        try:
            if self.capture is None:
                self.capture = mss.mss()

            img = self.capture.grab(self.region)
            frame = np.array(img)

        except Exception as e:
            logger.warning(f"MSS capture failed: {e}, trying pyautogui fallback")

            # Method 2: Fallback to pyautogui (more compatible)
            try:
                screenshot = pyautogui.screenshot(region=(
                    self.region["left"],
                    self.region["top"],
                    self.region["width"],
                    self.region["height"]
                ))
                frame = np.array(screenshot)
                # Convert RGB to BGRA for consistency
                frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGRA)

            except Exception as e2:
                logger.error(f"Pyautogui capture also failed: {e2}")
                # Reinitialize mss for next attempt
                try:
                    if self.capture:
                        self.capture.close()
                    self.capture = None
                except:
                    pass
                return

        # Store the captured frame
        if frame is not None:
            with self.lock:
                self.frame = frame
    
    def get_frame(self):
        '''
        Safely get latest game window frame
        '''
        with self.lock:
            if self.frame is None:
                return None
            # Convert BGRA to BGR
            return cv2.cvtColor(self.frame, cv2.COLOR_BGRA2BGR)
    
    def on_closed(self):
        '''
        Capture closed callback
        '''
        logger.warning("Capture session closed.")
        cv2.destroyAllWindows()
    
    def limit_fps(self):
        '''
        Limit FPS to save system resources
        '''
        # If the loop finished early, sleep to maintain target FPS
        target_duration = 1.0 / self.fps_limit  # seconds per frame
        frame_duration = time.time() - self.t_last_run
        if frame_duration < target_duration:
            time.sleep(target_duration - frame_duration)
        
        # Update FPS
        self.fps = round(1.0 / (time.time() - self.t_last_run))
        self.t_last_run = time.time()
